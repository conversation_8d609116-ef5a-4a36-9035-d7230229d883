import { useMedicalFacultiesStore } from '../stores/MedicalFacultiesStores'
import { MedicalSentencePayload } from '../types'
import { useSaveMedicalSentence } from './useSaveMedicalSentence'
import { useUpdateMedicalSentence } from './useUpdateMedicalSentence'

export const useMedicalSentenceStore = () => {
  const { saveMedicalSentenceMutation } = useSaveMedicalSentence()

  const { updateMedicalSentenceMutation } = useUpdateMedicalSentence()

  const { idReport, saveIdRecord } = useMedicalFacultiesStore()

  const saveMedicalSentence = (payload: MedicalSentencePayload, facultyId: string) => {
    if (idReport) {
      updateMedicalSentenceMutation({ payload, medicalSentenceId: idReport })
    } else {
      saveMedicalSentenceMutation(
        { payload, facultyId },
        {
          onSuccess: (data) => {
            console.log('>>>>>', data)
            const idRecord = data.result.id
            saveIdRecord(idRecord)
          },
          onError: () => {
            console.error('Failed to save medical sentence')
          },
        },
      )
    }
  }

  return {
    saveMedicalSentence,
  }
}
